import React, { useState, useEffect, useCallback } from 'react';
import { Select, message } from 'antd';
import type { TaskBasicGroupFormData } from '../../types/task';
import { TaskService } from '../../services/taskService';

// 任务分组选择组件属性接口
interface TaskGroupSelectProps {
  /** 当前选中的值 */
  value?: string;
  /** 值变化回调 */
  onChange?: (value: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否允许清除 */
  allowClear?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示搜索功能 */
  showSearch?: boolean;
  /** 选择框大小 */
  size?: 'small' | 'middle' | 'large';
  /** 是否只显示已使用的分组 */
  onlyUsed?: boolean;
  /** 是否只显示未使用的分组 */
  onlyUnused?: boolean;
}

/**
 * 任务分组选择组件
 * 
 * 功能特性：
 * - 自动加载任务分组数据
 * - 支持搜索过滤
 * - 支持清除选择
 * - 支持按使用状态过滤
 * - 加载状态显示
 * - 错误处理
 * 
 * @example
 * ```tsx
 * // 基础用法
 * <TaskGroupSelect 
 *   value={selectedGroup} 
 *   onChange={setSelectedGroup} 
 *   placeholder="请选择任务分组" 
 * />
 * 
 * // 只显示已使用的分组
 * <TaskGroupSelect 
 *   onlyUsed={true}
 *   placeholder="请选择已使用的分组" 
 * />
 * 
 * // 在表单中使用
 * <Form.Item name="group" label="任务分组">
 *   <TaskGroupSelect />
 * </Form.Item>
 * ```
 */
export const TaskGroupSelect: React.FC<TaskGroupSelectProps> = ({
  value,
  onChange,
  placeholder = '请选择任务分组',
  allowClear = true,
  className = 'w-full rounded-md',
  style,
  disabled = false,
  showSearch = true,
  size = 'middle',
  onlyUsed = false,
  onlyUnused = false,
}) => {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<{ label: string; value: string; disabled?: boolean }[]>([]);

  // 加载任务分组数据
  const loadTaskGroups = useCallback(async () => {
    setLoading(true);
    try {
      // 构建查询参数
      const params: any = {};
      if (onlyUsed) {
        params.is_used = true;
      } else if (onlyUnused) {
        params.is_used = false;
      }


      const response = await TaskService.getTaskGroups(params);
      
      // 转换为 Select 组件需要的选项格式
      const groupOptions = response.data.map((group: TaskBasicGroupFormData) => ({
        label: group.name,
        value: group.name,
        // 可以根据需要添加禁用逻辑
        disabled: false,
      }));
      
      setOptions(groupOptions);
    } catch (error) {
      console.error('加载任务分组失败:', error);
      message.error('加载任务分组失败: ' + error);
      setOptions([]);
    } finally {
      setLoading(false);
    }
  }, [onlyUsed, onlyUnused]);

  // 组件挂载时加载数据
  useEffect(() => {
    loadTaskGroups();
  }, [loadTaskGroups]);

  // 刷新数据的方法（可以通过 ref 调用）
  const refresh = useCallback(() => {
    loadTaskGroups();
  }, [loadTaskGroups]);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      className={className}
      style={style}
      disabled={disabled}
      loading={loading}
      options={options}
      showSearch={showSearch}
      size={size}
      filterOption={(input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
      }
      notFoundContent={loading ? '加载中...' : '暂无数据'}
    />
  );
};

export default TaskGroupSelect;
